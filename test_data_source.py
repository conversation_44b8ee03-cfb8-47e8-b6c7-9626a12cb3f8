#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_data_source():
    url = "https://data.17500.cn/3d_asc.txt"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    try:
        print(f"测试数据源: {url}")
        response = requests.get(url, headers=headers, verify=False, timeout=30)
        
        print(f"状态码: {response.status_code}")
        print(f"内容长度: {len(response.text)}")
        print(f"内容类型: {response.headers.get('content-type', 'unknown')}")
        
        if response.text:
            lines = response.text.strip().split('\n')
            print(f"行数: {len(lines)}")
            print("前5行内容:")
            for i, line in enumerate(lines[:5]):
                print(f"  {i+1}: {repr(line)}")

            print("\n最新5行内容:")
            for i, line in enumerate(lines[-5:]):
                print(f"  {len(lines)-4+i}: {repr(line)}")

            # 解析最新一行
            if lines:
                latest_line = lines[-1].strip()
                parts = latest_line.split()
                if len(parts) >= 3:
                    print(f"\n最新期号: {parts[0]}")
                    print(f"开奖日期: {parts[1]}")
                    print(f"开奖号码: {parts[2]}")
        else:
            print("内容为空")
            
    except Exception as e:
        print(f"请求失败: {e}")

if __name__ == "__main__":
    test_data_source()
