// 请求缓存工具 - 减少重复API调用，降低服务器压力
interface CacheItem {
  data: any
  timestamp: number
  expiry: number
}

class RequestCache {
  private cache = new Map<string, CacheItem>()
  private defaultTTL = 60000 // 默认缓存1分钟

  // 设置缓存
  set(key: string, data: any, ttl: number = this.defaultTTL): void {
    const now = Date.now()
    this.cache.set(key, {
      data,
      timestamp: now,
      expiry: now + ttl
    })
  }

  // 获取缓存
  get(key: string): any | null {
    const item = this.cache.get(key)
    if (!item) return null

    const now = Date.now()
    if (now > item.expiry) {
      this.cache.delete(key)
      return null
    }

    return item.data
  }

  // 检查缓存是否存在且有效
  has(key: string): boolean {
    return this.get(key) !== null
  }

  // 清除过期缓存
  cleanup(): void {
    const now = Date.now()
    for (const [key, item] of this.cache.entries()) {
      if (now > item.expiry) {
        this.cache.delete(key)
      }
    }
  }

  // 清除所有缓存
  clear(): void {
    this.cache.clear()
  }

  // 获取缓存统计信息
  getStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    }
  }
}

// 创建全局缓存实例
export const requestCache = new RequestCache()

// 定期清理过期缓存
setInterval(() => {
  requestCache.cleanup()
}, 300000) // 每5分钟清理一次

// 缓存配置
export const CACHE_CONFIG = {
  // API缓存时间配置（毫秒）
  SYSTEM_STATUS: 120000,      // 系统状态缓存2分钟
  PREDICTIONS: 180000,        // 预测数据缓存3分钟
  STATISTICS: 300000,         // 统计数据缓存5分钟
  MONITORING_TASKS: 60000,    // 监控任务缓存1分钟
  PERFORMANCE_METRICS: 120000 // 性能指标缓存2分钟
}

// 生成缓存键
export const generateCacheKey = (endpoint: string, params?: Record<string, any>): string => {
  if (!params) return endpoint
  
  const sortedParams = Object.keys(params)
    .sort()
    .map(key => `${key}=${params[key]}`)
    .join('&')
  
  return `${endpoint}?${sortedParams}`
}

// 带缓存的请求函数
export const cachedRequest = async (
  endpoint: string,
  params?: Record<string, any>,
  ttl?: number
): Promise<any> => {
  const cacheKey = generateCacheKey(endpoint, params)
  
  // 尝试从缓存获取
  const cached = requestCache.get(cacheKey)
  if (cached) {
    console.log(`🎯 缓存命中: ${cacheKey}`)
    return cached
  }

  // 缓存未命中，发起请求
  console.log(`🌐 发起请求: ${cacheKey}`)
  
  try {
    const url = params 
      ? `${endpoint}?${new URLSearchParams(params).toString()}`
      : endpoint
    
    const response = await fetch(url)
    const data = await response.json()
    
    // 存入缓存
    if (ttl) {
      requestCache.set(cacheKey, data, ttl)
    }
    
    return data
  } catch (error) {
    console.error(`❌ 请求失败: ${cacheKey}`, error)
    throw error
  }
}
