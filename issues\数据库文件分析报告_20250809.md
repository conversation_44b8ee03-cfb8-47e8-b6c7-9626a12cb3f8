# 福彩3D项目数据库文件分析报告

## 📋 报告概览

**分析时间**: 2025年8月9日  
**分析目录**: `D:\github\fucai3d\data`  
**数据库数量**: 3个  
**分析工具**: SQLite3 + Python分析脚本  

## 🗄️ 数据库文件详细分析

### 1. lottery.db - 历史开奖数据库

#### 📊 基本信息
- **文件路径**: `data/lottery.db`
- **数据库大小**: 约1.2MB
- **主要用途**: 存储福彩3D历史开奖数据
- **数据来源**: 17500.cn官方数据

#### 📋 表结构
```sql
-- 主要表：lottery_records
CREATE TABLE lottery_records (
    id INTEGER PRIMARY KEY,
    period TEXT NOT NULL,        -- 期号 (如: 2024001)
    numbers TEXT NOT NULL,       -- 开奖号码 (如: "123")
    date TEXT NOT NULL,          -- 开奖日期
    -- 其他辅助字段...
);
```

#### 📈 数据统计
- **记录数量**: 8359条真实历史记录
- **数据范围**: 从早期期号到最新期号
- **数据完整性**: 100%（无缺失记录）

#### 🔗 使用文件
- `src/data/sum_data_access.py` - 和值数据访问
- `src/data/complete_collector.py` - 完整数据采集
- `src/database/models.py` - 数据库模型
- `production/src/database/models.py` - 生产环境模型
- `config/fusion_config.yaml` - 融合系统配置
- `config/*_predictor_config.yaml` - 各预测器配置

#### 🎯 核心作用
- **P3-P5预测器**: 提供历史数据进行模型训练
- **特征工程**: 生成统计特征和模式分析
- **数据验证**: 验证预测结果的准确性

---

### 2. fucai3d.db - 主业务数据库

#### 📊 基本信息
- **文件路径**: `data/fucai3d.db`
- **数据库大小**: 约156KB
- **主要用途**: 存储预测结果、性能监控、系统配置
- **表数量**: 15个业务表

#### 📋 核心表结构

##### final_predictions - 最终预测结果表
```sql
CREATE TABLE final_predictions (
    id INTEGER PRIMARY KEY,
    issue TEXT NOT NULL,              -- 期号
    prediction_rank INTEGER,          -- 预测排名
    hundreds INTEGER,                 -- 百位预测
    tens INTEGER,                     -- 十位预测
    units INTEGER,                    -- 个位预测
    sum_value INTEGER,                -- 和值
    span_value INTEGER,               -- 跨度
    combined_probability REAL,        -- 综合概率
    confidence_level TEXT,            -- 置信度等级
    fusion_method TEXT,               -- 融合方法
    ranking_strategy TEXT,            -- 排名策略
    created_at TIMESTAMP              -- 创建时间
);
```

##### hundreds_predictions - 百位预测表
```sql
CREATE TABLE hundreds_predictions (
    id INTEGER PRIMARY KEY,
    issue TEXT NOT NULL,              -- 期号
    model_type TEXT,                  -- 模型类型
    prob_0 REAL, prob_1 REAL, ...,   -- 各数字概率
    predicted_digit INTEGER,          -- 预测数字
    confidence REAL,                  -- 置信度
    created_at TIMESTAMP              -- 创建时间
);
```

##### optimization_logs - 优化日志表
```sql
CREATE TABLE optimization_logs (
    id INTEGER PRIMARY KEY,
    optimization_type TEXT,           -- 优化类型
    trigger_reason TEXT,              -- 触发原因
    component_name TEXT,              -- 组件名称
    start_time TIMESTAMP,             -- 开始时间
    end_time TIMESTAMP,               -- 结束时间
    status TEXT,                      -- 状态
    performance_before TEXT,          -- 优化前性能
    performance_after TEXT,           -- 优化后性能
    improvement_score REAL            -- 改进分数
);
```

#### 📈 数据统计
- **final_predictions**: 20条预测记录（当前期号2025209）
- **fusion_weights**: 6条权重配置
- **fusion_constraint_rules**: 5条约束规则
- **其他表**: 大部分为空（待系统运行后填充）

#### 🔗 使用文件
- `src/web/app.py` - Web应用主程序（第79行）
- `src/web/routes/prediction.py` - 预测API路由
- `src/optimization/` - P9优化系统
- `src/fusion/` - P8融合系统

#### 🎯 核心作用
- **P8融合系统**: 存储融合权重、约束规则、融合会话
- **P9优化系统**: 记录优化日志、性能监控、配置管理
- **P10 Web系统**: 提供预测结果、系统状态数据
- **性能监控**: 记录系统运行指标和告警信息

---

### 3. alerts.db - 监控告警数据库

#### 📊 基本信息
- **文件路径**: `data/alerts.db`
- **数据库大小**: 约8KB
- **主要用途**: 存储系统监控告警信息
- **表数量**: 2个表

#### 📋 表结构
```sql
-- 告警记录表
CREATE TABLE alerts (
    id INTEGER PRIMARY KEY,
    alert_type TEXT NOT NULL,         -- 告警类型
    level TEXT NOT NULL,              -- 告警级别 (INFO/WARNING/ERROR)
    message TEXT NOT NULL,            -- 告警消息
    timestamp DATETIME                -- 告警时间
);
```

#### 📈 数据统计
- **alerts表**: 1条测试告警记录
- **示例数据**: `('test_alert', 'INFO', '监控系统测试告警', '2025-08-07 06:23:14')`

#### 🔗 使用文件
- `scripts/alert_system.py` - 告警系统脚本（第42行）
- `scripts/start_monitoring.py` - 监控启动脚本
- `.yoyo/snapshot/scripts/start_monitoring.py` - 快照脚本

#### 🎯 核心作用
- **系统监控**: 记录系统运行异常和告警
- **性能告警**: 监控系统性能指标超阈值情况
- **运维支持**: 为系统运维提供告警历史记录

## 🔄 数据库使用关系图

```
lottery.db (历史数据)
    ↓
P3-P5预测器 (训练模型)
    ↓
fucai3d.db (预测结果)
    ↓
P8融合系统 (融合预测)
    ↓
P9优化系统 (性能优化)
    ↓
P10 Web系统 (结果展示)
    ↓
alerts.db (监控告警)
```

## 📊 数据流向分析

### 输入数据流
1. **历史数据采集**: `lottery.db` ← 17500.cn数据源
2. **特征工程**: `lottery.db` → 特征提取 → 模型训练
3. **预测生成**: 训练模型 → `fucai3d.db` (预测结果)

### 处理数据流
1. **P3-P5预测**: `lottery.db` → 独立预测 → `fucai3d.db`
2. **P8融合**: `fucai3d.db` → 融合处理 → `fucai3d.db`
3. **P9优化**: `fucai3d.db` → 性能分析 → `fucai3d.db`

### 输出数据流
1. **Web展示**: `fucai3d.db` → API → 前端界面
2. **监控告警**: 系统状态 → `alerts.db` → 告警通知
3. **性能报告**: `fucai3d.db` → 性能分析 → 优化建议

## 🎯 关键发现

### ✅ 优势
1. **数据完整性**: lottery.db包含8359条真实历史数据
2. **架构清晰**: 三个数据库职责分明，互不干扰
3. **扩展性好**: fucai3d.db设计了完整的业务表结构
4. **监控完善**: alerts.db提供系统监控支持

### ⚠️ 注意事项
1. **数据同步**: 需要定期更新lottery.db的历史数据
2. **性能监控**: fucai3d.db中的监控表需要定期清理
3. **备份策略**: 建议对lottery.db进行定期备份
4. **索引优化**: 大表需要创建适当的索引提升查询性能

## 📋 维护建议

### 日常维护
1. **数据更新**: 每日更新lottery.db的最新开奖数据
2. **性能监控**: 监控fucai3d.db的表大小和查询性能
3. **告警处理**: 及时处理alerts.db中的告警信息

### 定期维护
1. **数据备份**: 每周备份lottery.db和fucai3d.db
2. **性能优化**: 每月分析查询性能，优化慢查询
3. **数据清理**: 每季度清理过期的监控和日志数据

---

**报告生成时间**: 2025年8月9日  
**分析工具**: Python SQLite3 + 代码检索  
**数据准确性**: 基于实际数据库文件分析  
