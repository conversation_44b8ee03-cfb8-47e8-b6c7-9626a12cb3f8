import React, { useState, useEffect } from 'react'
import { Card, Statistic, Row, Col, Progress, Tag, Tooltip, Button } from 'antd'
import { 
  ThunderboltOutlined, 
  DatabaseOutlined, 
  ReloadOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined 
} from '@ant-design/icons'
import { requestCache } from '../utils/requestCache'

interface RequestStats {
  totalRequests: number
  cacheHits: number
  cacheMisses: number
  cacheSize: number
  hitRate: number
}

const RequestMonitor: React.FC = () => {
  const [stats, setStats] = useState<RequestStats>({
    totalRequests: 0,
    cacheHits: 0,
    cacheMisses: 0,
    cacheSize: 0,
    hitRate: 0
  })

  const [isVisible, setIsVisible] = useState(false)

  // 模拟统计数据更新（实际项目中应该从真实的请求拦截器获取）
  useEffect(() => {
    const updateStats = () => {
      const cacheStats = requestCache.getStats()
      
      // 这里应该从实际的请求统计中获取数据
      // 目前使用模拟数据演示
      setStats(prevStats => {
        const newCacheHits = prevStats.cacheHits + Math.floor(Math.random() * 3)
        const newCacheMisses = prevStats.cacheMisses + Math.floor(Math.random() * 1)
        const totalRequests = newCacheHits + newCacheMisses
        
        return {
          totalRequests,
          cacheHits: newCacheHits,
          cacheMisses: newCacheMisses,
          cacheSize: cacheStats.size,
          hitRate: totalRequests > 0 ? (newCacheHits / totalRequests) * 100 : 0
        }
      })
    }

    const interval = setInterval(updateStats, 5000) // 每5秒更新一次统计
    return () => clearInterval(interval)
  }, [])

  const handleClearCache = () => {
    requestCache.clear()
    setStats(prev => ({ ...prev, cacheSize: 0 }))
  }

  const getHitRateColor = (rate: number) => {
    if (rate >= 80) return '#52c41a' // 绿色 - 优秀
    if (rate >= 60) return '#faad14' // 黄色 - 良好
    return '#ff4d4f' // 红色 - 需要优化
  }

  const getHitRateStatus = (rate: number) => {
    if (rate >= 80) return 'success'
    if (rate >= 60) return 'warning'
    return 'exception'
  }

  if (!isVisible) {
    return (
      <Button 
        type="text" 
        size="small"
        icon={<DatabaseOutlined />}
        onClick={() => setIsVisible(true)}
        style={{ position: 'fixed', top: 10, right: 10, zIndex: 1000 }}
      >
        请求监控
      </Button>
    )
  }

  return (
    <Card
      title={
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <span>
            <DatabaseOutlined style={{ marginRight: 8 }} />
            请求性能监控
          </span>
          <Button 
            type="text" 
            size="small"
            onClick={() => setIsVisible(false)}
          >
            ×
          </Button>
        </div>
      }
      size="small"
      style={{ 
        position: 'fixed', 
        top: 10, 
        right: 10, 
        width: 400,
        zIndex: 1000,
        boxShadow: '0 4px 12px rgba(0,0,0,0.15)'
      }}
      extra={
        <Tooltip title="清除缓存">
          <Button 
            type="text" 
            size="small"
            icon={<ReloadOutlined />}
            onClick={handleClearCache}
          />
        </Tooltip>
      }
    >
      <Row gutter={[16, 16]}>
        <Col span={12}>
          <Statistic
            title="总请求数"
            value={stats.totalRequests}
            prefix={<ThunderboltOutlined />}
            valueStyle={{ fontSize: '16px' }}
          />
        </Col>
        <Col span={12}>
          <Statistic
            title="缓存大小"
            value={stats.cacheSize}
            prefix={<DatabaseOutlined />}
            suffix="项"
            valueStyle={{ fontSize: '16px' }}
          />
        </Col>
      </Row>

      <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
        <Col span={24}>
          <div style={{ marginBottom: 8 }}>
            <span style={{ fontSize: '14px', color: '#666' }}>缓存命中率</span>
            <Tag 
              color={getHitRateColor(stats.hitRate)}
              style={{ float: 'right' }}
            >
              {stats.hitRate.toFixed(1)}%
            </Tag>
          </div>
          <Progress
            percent={stats.hitRate}
            status={getHitRateStatus(stats.hitRate)}
            strokeColor={getHitRateColor(stats.hitRate)}
            size="small"
            showInfo={false}
          />
        </Col>
      </Row>

      <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
        <Col span={12}>
          <div style={{ textAlign: 'center' }}>
            <div style={{ color: '#52c41a', fontSize: '18px', fontWeight: 'bold' }}>
              {stats.cacheHits}
            </div>
            <div style={{ fontSize: '12px', color: '#666' }}>
              <CheckCircleOutlined style={{ marginRight: 4 }} />
              缓存命中
            </div>
          </div>
        </Col>
        <Col span={12}>
          <div style={{ textAlign: 'center' }}>
            <div style={{ color: '#ff4d4f', fontSize: '18px', fontWeight: 'bold' }}>
              {stats.cacheMisses}
            </div>
            <div style={{ fontSize: '12px', color: '#666' }}>
              <ExclamationCircleOutlined style={{ marginRight: 4 }} />
              缓存未命中
            </div>
          </div>
        </Col>
      </Row>

      <div style={{ marginTop: 16, padding: 8, backgroundColor: '#f5f5f5', borderRadius: 4 }}>
        <div style={{ fontSize: '12px', color: '#666', textAlign: 'center' }}>
          💡 缓存优化已启用，大幅减少服务器请求压力
        </div>
      </div>
    </Card>
  )
}

export default RequestMonitor
