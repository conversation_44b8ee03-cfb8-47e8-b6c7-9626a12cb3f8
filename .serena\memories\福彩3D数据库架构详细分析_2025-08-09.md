# 福彩3D数据库架构详细分析

## 数据库文件概览

### 1. lottery.db - 历史开奖数据库
**文件路径**: `data/lottery.db`
**数据库大小**: 约1.2MB
**核心作用**: P3-P5预测器训练的主要数据源

#### 表结构
- **lottery_records表**: 主要历史数据表
  - period (TEXT): 期号 (如: 2024001)
  - numbers (TEXT): 开奖号码 (如: "123")
  - date (TEXT): 开奖日期
  - 其他辅助字段

#### 数据统计
- **记录数量**: 8359条真实历史记录
- **数据来源**: 17500.cn官方数据
- **数据完整性**: 100%无缺失
- **数据范围**: 从早期期号到最新期号

#### 使用文件
- `src/data/sum_data_access.py` - 和值数据访问
- `src/data/complete_collector.py` - 完整数据采集
- `src/database/models.py` - 数据库模型
- `config/fusion_config.yaml` - 融合系统配置
- `config/*_predictor_config.yaml` - 各预测器配置

### 2. fucai3d.db - 主业务数据库
**文件路径**: `data/fucai3d.db`
**数据库大小**: 约156KB
**核心作用**: P8融合、P9优化、P10 Web系统的核心数据存储

#### 核心表结构
##### final_predictions - 最终预测结果表
- id (INTEGER): 主键
- issue (TEXT): 期号
- prediction_rank (INTEGER): 预测排名
- hundreds/tens/units (INTEGER): 百十个位预测
- sum_value/span_value (INTEGER): 和值/跨度
- combined_probability (REAL): 综合概率
- confidence_level (TEXT): 置信度等级
- fusion_method (TEXT): 融合方法
- ranking_strategy (TEXT): 排名策略
- created_at (TIMESTAMP): 创建时间

##### hundreds_predictions - 百位预测表
- issue (TEXT): 期号
- model_type (TEXT): 模型类型
- prob_0 到 prob_9 (REAL): 各数字概率
- predicted_digit (INTEGER): 预测数字
- confidence (REAL): 置信度

##### optimization_logs - 优化日志表
- optimization_type (TEXT): 优化类型
- trigger_reason (TEXT): 触发原因
- component_name (TEXT): 组件名称
- performance_before/after (TEXT): 优化前后性能
- improvement_score (REAL): 改进分数

##### fusion_weights - 融合权重表
- weight_type (TEXT): 权重类型
- predictor_name (TEXT): 预测器名称
- weight_value (REAL): 权重值
- performance_score (REAL): 性能分数

#### 数据统计
- **总表数**: 15个业务表
- **final_predictions**: 20条预测记录 (期号2025209)
- **fusion_weights**: 6条权重配置
- **fusion_constraint_rules**: 5条约束规则
- **其他表**: 大部分为空，待系统运行后填充

#### 使用文件
- `src/web/app.py` - Web应用主程序 (第79行)
- `src/web/routes/prediction.py` - 预测API路由
- `src/optimization/` - P9优化系统
- `src/fusion/` - P8融合系统

### 3. alerts.db - 监控告警数据库
**文件路径**: `data/alerts.db`
**数据库大小**: 约8KB
**核心作用**: 系统监控和告警管理

#### 表结构
##### alerts - 告警记录表
- id (INTEGER): 主键
- alert_type (TEXT): 告警类型
- level (TEXT): 告警级别 (INFO/WARNING/ERROR)
- message (TEXT): 告警消息
- timestamp (DATETIME): 告警时间

#### 数据统计
- **alerts表**: 1条测试告警记录
- **示例数据**: test_alert, INFO, 监控系统测试告警, 2025-08-07

#### 使用文件
- `scripts/alert_system.py` - 告警系统脚本
- `scripts/start_monitoring.py` - 监控启动脚本

## 数据流向关系

### 核心数据流
1. **历史数据采集**: 17500.cn → lottery.db
2. **模型训练**: lottery.db → P3-P5预测器
3. **预测生成**: P3-P5预测器 → fucai3d.db
4. **融合处理**: fucai3d.db → P8融合系统 → fucai3d.db
5. **优化分析**: fucai3d.db → P9优化系统 → fucai3d.db
6. **结果展示**: fucai3d.db → P10 Web系统
7. **监控告警**: 系统状态 → alerts.db

### 系统架构关系
```
lottery.db (历史数据) → P3-P5预测器 → fucai3d.db (业务数据)
                                           ↓
alerts.db (监控) ← P10 Web ← P9优化 ← P8融合
```

## 关键技术要点

### 数据库连接配置
- **lottery.db**: 主要用于只读访问，提供历史数据
- **fucai3d.db**: 读写访问，存储实时业务数据
- **alerts.db**: 写入为主，记录系统监控信息

### 性能优化建议
1. **lottery.db**: 创建期号和日期索引，优化查询性能
2. **fucai3d.db**: 定期清理过期日志，控制数据库大小
3. **alerts.db**: 设置告警数据保留期限，避免无限增长

### 维护要点
1. **数据同步**: 定期更新lottery.db的最新开奖数据
2. **备份策略**: 重点备份lottery.db和fucai3d.db
3. **监控清理**: 定期清理alerts.db的过期告警记录

## 开发注意事项

### 数据库路径配置
- 所有配置文件统一使用相对路径 `data/lottery.db`
- Web应用使用绝对路径确保连接正确
- 生产环境需要调整数据库路径配置

### 事务处理
- lottery.db: 只读访问，无需事务控制
- fucai3d.db: 预测结果写入需要事务保护
- alerts.db: 告警写入使用简单事务

### 并发控制
- SQLite支持多读单写，注意写操作的并发控制
- 长时间查询可能阻塞写操作，需要优化查询语句
- 考虑使用连接池管理数据库连接

---

**分析完成时间**: 2025年8月9日
**数据准确性**: 基于实际数据库文件分析
**更新频率**: 建议每月更新一次数据库结构分析