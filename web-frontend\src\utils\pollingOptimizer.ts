import React from 'react'

// 轮询优化器 - 统一管理所有API轮询，避免重复请求
interface PollingConfig {
  endpoint: string
  interval: number
  enabled: boolean
  lastRequest?: number
  data?: any
}

interface PollingSubscriber {
  id: string
  callback: (data: any) => void
  endpoints: string[]
}

class PollingOptimizer {
  private configs: Map<string, PollingConfig> = new Map()
  private subscribers: Map<string, PollingSubscriber> = new Map()
  private timers: Map<string, NodeJS.Timeout> = new Map()
  private isWebSocketConnected = false

  constructor() {
    this.initializeConfigs()
    this.checkWebSocketStatus()
  }

  private initializeConfigs() {
    // 配置所有需要轮询的接口
    const defaultConfigs: PollingConfig[] = [
      {
        endpoint: '/api/status',
        interval: 300000, // 5分钟
        enabled: true
      },
      {
        endpoint: '/api/prediction/latest',
        interval: 300000, // 5分钟
        enabled: true
      },
      {
        endpoint: '/api/monitoring/tasks',
        interval: 180000, // 3分钟
        enabled: true
      },
      {
        endpoint: '/api/prediction/statistics',
        interval: 600000, // 10分钟
        enabled: true
      }
    ]

    defaultConfigs.forEach(config => {
      this.configs.set(config.endpoint, config)
    })
  }

  private checkWebSocketStatus() {
    // 检查WebSocket连接状态
    const wsUrl = 'ws://127.0.0.1:8000/ws'
    const ws = new WebSocket(wsUrl)
    
    ws.onopen = () => {
      console.log('🔗 WebSocket连接成功，禁用轮询')
      this.isWebSocketConnected = true
      this.disableAllPolling()
    }

    ws.onerror = () => {
      console.log('❌ WebSocket连接失败，启用轮询')
      this.isWebSocketConnected = false
      this.enablePolling()
    }

    ws.onclose = () => {
      console.log('🔌 WebSocket连接断开，启用轮询')
      this.isWebSocketConnected = false
      this.enablePolling()
    }
  }

  // 订阅数据更新
  subscribe(id: string, endpoints: string[], callback: (data: any) => void) {
    console.log(`📡 订阅数据更新: ${id} -> ${endpoints.join(', ')}`)
    
    this.subscribers.set(id, {
      id,
      callback,
      endpoints
    })

    // 如果WebSocket未连接，启动轮询
    if (!this.isWebSocketConnected) {
      endpoints.forEach(endpoint => {
        this.startPolling(endpoint)
      })
    }
  }

  // 取消订阅
  unsubscribe(id: string) {
    console.log(`🚫 取消订阅: ${id}`)
    this.subscribers.delete(id)
    
    // 检查是否还有其他订阅者，如果没有则停止轮询
    this.checkAndStopUnusedPolling()
  }

  private startPolling(endpoint: string) {
    const config = this.configs.get(endpoint)
    if (!config || !config.enabled) return

    // 避免重复启动
    if (this.timers.has(endpoint)) {
      console.log(`⚠️ 轮询已存在: ${endpoint}`)
      return
    }

    console.log(`🔄 启动轮询: ${endpoint} (间隔: ${config.interval}ms)`)

    const poll = async () => {
      try {
        const now = Date.now()
        
        // 防止过于频繁的请求
        if (config.lastRequest && (now - config.lastRequest) < config.interval) {
          console.log(`⏰ 跳过过于频繁的请求: ${endpoint}`)
          return
        }

        config.lastRequest = now
        console.log(`🌐 发起轮询请求: ${endpoint}`)

        const response = await fetch(endpoint)
        const data = await response.json()
        
        config.data = data
        
        // 通知所有订阅者
        this.notifySubscribers(endpoint, data)
        
      } catch (error) {
        console.error(`❌ 轮询请求失败: ${endpoint}`, error)
      }
    }

    // 立即执行一次
    poll()

    // 设置定时器
    const timer = setInterval(poll, config.interval)
    this.timers.set(endpoint, timer)
  }

  private stopPolling(endpoint: string) {
    const timer = this.timers.get(endpoint)
    if (timer) {
      console.log(`⏹️ 停止轮询: ${endpoint}`)
      clearInterval(timer)
      this.timers.delete(endpoint)
    }
  }

  private notifySubscribers(endpoint: string, data: any) {
    this.subscribers.forEach(subscriber => {
      if (subscriber.endpoints.includes(endpoint)) {
        try {
          subscriber.callback({ endpoint, data })
        } catch (error) {
          console.error(`❌ 通知订阅者失败: ${subscriber.id}`, error)
        }
      }
    })
  }

  private checkAndStopUnusedPolling() {
    // 检查每个轮询是否还有订阅者
    this.configs.forEach((config, endpoint) => {
      const hasSubscribers = Array.from(this.subscribers.values())
        .some(sub => sub.endpoints.includes(endpoint))
      
      if (!hasSubscribers) {
        this.stopPolling(endpoint)
      }
    })
  }

  private disableAllPolling() {
    console.log('🛑 禁用所有轮询（WebSocket已连接）')
    this.timers.forEach((timer, endpoint) => {
      this.stopPolling(endpoint)
    })
  }

  private enablePolling() {
    console.log('🔄 启用轮询（WebSocket未连接）')
    // 为所有有订阅者的端点启动轮询
    this.configs.forEach((config, endpoint) => {
      const hasSubscribers = Array.from(this.subscribers.values())
        .some(sub => sub.endpoints.includes(endpoint))
      
      if (hasSubscribers) {
        this.startPolling(endpoint)
      }
    })
  }

  // 获取缓存的数据
  getCachedData(endpoint: string) {
    const config = this.configs.get(endpoint)
    return config?.data
  }

  // 获取统计信息
  getStats() {
    return {
      activePolling: this.timers.size,
      subscribers: this.subscribers.size,
      webSocketConnected: this.isWebSocketConnected,
      configs: Array.from(this.configs.entries()).map(([endpoint, config]) => ({
        endpoint,
        interval: config.interval,
        enabled: config.enabled,
        lastRequest: config.lastRequest
      }))
    }
  }

  // 手动触发数据刷新
  async refresh(endpoint: string) {
    const config = this.configs.get(endpoint)
    if (!config) return

    try {
      console.log(`🔄 手动刷新: ${endpoint}`)
      const response = await fetch(endpoint)
      const data = await response.json()
      
      config.data = data
      config.lastRequest = Date.now()
      
      this.notifySubscribers(endpoint, data)
      return data
    } catch (error) {
      console.error(`❌ 手动刷新失败: ${endpoint}`, error)
      throw error
    }
  }
}

// 创建全局实例
export const pollingOptimizer = new PollingOptimizer()

// 导出便捷的Hook
export const useOptimizedPolling = (
  id: string, 
  endpoints: string[], 
  callback: (data: any) => void
) => {
  React.useEffect(() => {
    pollingOptimizer.subscribe(id, endpoints, callback)
    
    return () => {
      pollingOptimizer.unsubscribe(id)
    }
  }, [id, endpoints.join(','), callback])

  return {
    refresh: (endpoint: string) => pollingOptimizer.refresh(endpoint),
    getCachedData: (endpoint: string) => pollingOptimizer.getCachedData(endpoint),
    getStats: () => pollingOptimizer.getStats()
  }
}

export default pollingOptimizer
