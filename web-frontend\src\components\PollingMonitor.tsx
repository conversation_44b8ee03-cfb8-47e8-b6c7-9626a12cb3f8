import React, { useState, useEffect } from 'react'
import { Card, Statistic, Row, Col, Button, Tag, Timeline, Alert } from 'antd'
import { 
  DatabaseOutlined, 
  ThunderboltOutlined, 
  ReloadOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ClockCircleOutlined
} from '@ant-design/icons'
import { pollingOptimizer } from '../utils/pollingOptimizer'

const PollingMonitor: React.FC = () => {
  const [stats, setStats] = useState<any>(null)
  const [logs, setLogs] = useState<string[]>([])
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    const updateStats = () => {
      const currentStats = pollingOptimizer.getStats()
      setStats(currentStats)
    }

    // 初始更新
    updateStats()

    // 定期更新统计信息
    const interval = setInterval(updateStats, 5000)

    // 监听控制台日志（简化版）
    const originalLog = console.log
    console.log = (...args) => {
      const message = args.join(' ')
      if (message.includes('🔄') || message.includes('🌐') || message.includes('🎯') || message.includes('❌')) {
        setLogs(prev => {
          const newLogs = [new Date().toLocaleTimeString() + ': ' + message, ...prev]
          return newLogs.slice(0, 10) // 只保留最近10条
        })
      }
      originalLog.apply(console, args)
    }

    return () => {
      clearInterval(interval)
      console.log = originalLog
    }
  }, [])

  if (!isVisible) {
    return (
      <Button 
        type="primary" 
        size="small"
        icon={<DatabaseOutlined />}
        onClick={() => setIsVisible(true)}
        style={{ 
          position: 'fixed', 
          bottom: 20, 
          right: 20, 
          zIndex: 1000,
          boxShadow: '0 4px 12px rgba(0,0,0,0.15)'
        }}
      >
        轮询监控
      </Button>
    )
  }

  const getStatusColor = (connected: boolean) => {
    return connected ? '#52c41a' : '#ff4d4f'
  }

  const getStatusIcon = (connected: boolean) => {
    return connected ? <CheckCircleOutlined /> : <ExclamationCircleOutlined />
  }

  return (
    <Card
      title={
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <span>
            <DatabaseOutlined style={{ marginRight: 8 }} />
            轮询优化监控
          </span>
          <Button 
            type="text" 
            size="small"
            onClick={() => setIsVisible(false)}
          >
            ×
          </Button>
        </div>
      }
      size="small"
      style={{ 
        position: 'fixed', 
        bottom: 20, 
        right: 20, 
        width: 500,
        maxHeight: 600,
        overflow: 'auto',
        zIndex: 1000,
        boxShadow: '0 4px 12px rgba(0,0,0,0.15)'
      }}
    >
      {stats && (
        <>
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <Statistic
                title="活跃轮询"
                value={stats.activePolling}
                prefix={<ThunderboltOutlined />}
                valueStyle={{ fontSize: '16px' }}
              />
            </Col>
            <Col span={12}>
              <Statistic
                title="订阅者数量"
                value={stats.subscribers}
                prefix={<DatabaseOutlined />}
                valueStyle={{ fontSize: '16px' }}
              />
            </Col>
          </Row>

          <div style={{ marginTop: 16, marginBottom: 16 }}>
            <div style={{ marginBottom: 8 }}>
              <span style={{ fontSize: '14px', color: '#666' }}>WebSocket状态</span>
              <Tag 
                color={getStatusColor(stats.webSocketConnected)}
                icon={getStatusIcon(stats.webSocketConnected)}
                style={{ float: 'right' }}
              >
                {stats.webSocketConnected ? '已连接' : '未连接'}
              </Tag>
            </div>
          </div>

          {stats.configs && stats.configs.length > 0 && (
            <div style={{ marginBottom: 16 }}>
              <div style={{ fontSize: '14px', fontWeight: 'bold', marginBottom: 8 }}>
                轮询配置
              </div>
              {stats.configs.map((config: any, index: number) => (
                <div key={index} style={{ 
                  padding: '8px', 
                  backgroundColor: '#f5f5f5', 
                  borderRadius: '4px',
                  marginBottom: '4px',
                  fontSize: '12px'
                }}>
                  <div style={{ fontWeight: 'bold' }}>{config.endpoint}</div>
                  <div style={{ color: '#666' }}>
                    间隔: {Math.round(config.interval / 1000)}秒
                    {config.lastRequest && (
                      <span style={{ marginLeft: 8 }}>
                        上次请求: {new Date(config.lastRequest).toLocaleTimeString()}
                      </span>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}

          {logs.length > 0 && (
            <div>
              <div style={{ fontSize: '14px', fontWeight: 'bold', marginBottom: 8 }}>
                <ClockCircleOutlined style={{ marginRight: 4 }} />
                最近日志
              </div>
              <div style={{ 
                maxHeight: '200px', 
                overflow: 'auto',
                backgroundColor: '#f5f5f5',
                padding: '8px',
                borderRadius: '4px'
              }}>
                {logs.map((log, index) => (
                  <div key={index} style={{ 
                    fontSize: '11px', 
                    marginBottom: '2px',
                    fontFamily: 'monospace'
                  }}>
                    {log}
                  </div>
                ))}
              </div>
            </div>
          )}

          <Alert
            message="轮询优化已启用"
            description="系统已自动优化API请求频率，大幅减少服务器压力。WebSocket连接时将自动禁用轮询。"
            type="success"
            showIcon
            style={{ marginTop: 16, fontSize: '12px' }}
          />
        </>
      )}
    </Card>
  )
}

export default PollingMonitor
