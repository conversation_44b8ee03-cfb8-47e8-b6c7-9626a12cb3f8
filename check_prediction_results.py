#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3

def check_prediction_results():
    """检查预测结果"""
    try:
        conn = sqlite3.connect('data/fucai3d.db')
        cursor = conn.cursor()

        # 先检查表结构
        cursor.execute("PRAGMA table_info(final_predictions)")
        columns = cursor.fetchall()
        print("final_predictions表结构:")
        for col in columns:
            print(f"  {col[1]} ({col[2]})")

        # 检查2025211期的预测结果
        cursor.execute('''
            SELECT * FROM final_predictions
            WHERE issue = "2025211"
            LIMIT 10
        ''')

        results = cursor.fetchall()

        if results:
            print(f"\n🎯 2025211期预测结果 (共{len(results)}条):")
            print("=" * 60)
            print("排名  号码  和值  跨度  综合概率   置信度")
            print("-" * 60)
            for row in results:
                rank = row[2]
                hundreds, tens, units = row[3], row[4], row[5]
                sum_val, span_val = row[6], row[7]
                combined_prob = row[8]
                confidence = row[18]
                print(f"{rank:2d}   {hundreds}{tens}{units}   {sum_val:2d}   {span_val:2d}    {combined_prob:.4f}     {confidence}")
        else:
            print("❌ 未找到2025211期的预测结果")

        conn.close()

    except Exception as e:
        print(f"❌ 查询失败: {e}")

if __name__ == "__main__":
    check_prediction_results()
