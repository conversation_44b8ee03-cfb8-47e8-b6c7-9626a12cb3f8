// 轮询配置 - 大幅减少服务器请求压力
export const POLLING_CONFIG = {
  // 全局开关 - 可以完全禁用轮询
  ENABLE_POLLING: false, // 🚫 完全禁用自动轮询，只允许手动刷新
  
  // 轮询间隔配置（毫秒）
  INTERVALS: {
    SYSTEM_STATUS: 1800000,     // 30分钟 - 系统状态
    PREDICTIONS: 1800000,       // 30分钟 - 预测数据  
    STATISTICS: 3600000,        // 60分钟 - 统计数据
    MONITORING_TASKS: 900000,   // 15分钟 - 监控任务
    PERFORMANCE_METRICS: 1800000 // 30分钟 - 性能指标
  },
  
  // WebSocket配置
  WEBSOCKET: {
    ENABLE: true,
    URL: 'ws://127.0.0.1:8000/ws',
    RECONNECT_INTERVAL: 30000,  // 30秒重连间隔
    MAX_RECONNECT_ATTEMPTS: 5
  },
  
  // 缓存配置
  CACHE: {
    ENABLE: true,
    DEFAULT_TTL: 300000,        // 5分钟默认缓存时间
    MAX_SIZE: 100               // 最大缓存项数
  },
  
  // 手动刷新配置
  MANUAL_REFRESH: {
    ENABLE: true,
    DEBOUNCE_TIME: 2000,        // 2秒防抖时间
    SHOW_LOADING: true
  }
}

// 获取轮询间隔
export const getPollingInterval = (type: keyof typeof POLLING_CONFIG.INTERVALS): number => {
  if (!POLLING_CONFIG.ENABLE_POLLING) {
    return Infinity // 返回无限大，实际上禁用轮询
  }
  return POLLING_CONFIG.INTERVALS[type]
}

// 检查是否启用轮询
export const isPollingEnabled = (): boolean => {
  return POLLING_CONFIG.ENABLE_POLLING
}

// 检查是否启用WebSocket
export const isWebSocketEnabled = (): boolean => {
  return POLLING_CONFIG.WEBSOCKET.ENABLE
}

// 获取WebSocket URL
export const getWebSocketUrl = (): string => {
  return POLLING_CONFIG.WEBSOCKET.URL
}

// 日志配置
export const LOG_CONFIG = {
  ENABLE_REQUEST_LOGS: true,
  ENABLE_CACHE_LOGS: true,
  ENABLE_WEBSOCKET_LOGS: true,
  LOG_LEVEL: 'info' as 'debug' | 'info' | 'warn' | 'error'
}

// 性能监控配置
export const PERFORMANCE_CONFIG = {
  ENABLE_MONITORING: true,
  TRACK_REQUEST_COUNT: true,
  TRACK_CACHE_HIT_RATE: true,
  TRACK_RESPONSE_TIME: true,
  ALERT_THRESHOLDS: {
    HIGH_REQUEST_RATE: 10,      // 每分钟超过10个请求时告警
    LOW_CACHE_HIT_RATE: 0.5,    // 缓存命中率低于50%时告警
    SLOW_RESPONSE_TIME: 5000    // 响应时间超过5秒时告警
  }
}

export default POLLING_CONFIG
