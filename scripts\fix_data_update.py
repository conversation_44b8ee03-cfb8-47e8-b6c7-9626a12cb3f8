#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据更新修复脚本
解决数据源429错误，实现智能重试和多源备份
"""

import os
import sys
import time
import sqlite3
import requests
import random
from datetime import datetime
from typing import List, Dict, Any, Optional

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

class DataUpdateFixer:
    """数据更新修复器"""
    
    def __init__(self):
        self.db_path = "data/lottery.db"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-C<PERSON>,zh;q=0.8,en-US;q=0.5,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        
        # 多个数据源
        self.data_sources = [
            "https://data.17500.cn/3d_asc.txt",
            "https://data.17500.cn/3d_desc.txt",
            "https://www.17500.cn/getData/3d.TXT",
        ]
    
    def safe_request(self, url: str, timeout: int = 30) -> Optional[requests.Response]:
        """安全的HTTP请求"""
        try:
            # 随机延迟，避免被限制
            time.sleep(random.uniform(2, 5))
            
            response = requests.get(
                url, 
                headers=self.headers, 
                timeout=timeout,
                verify=False  # 忽略SSL验证
            )
            
            if response.status_code == 200:
                print(f"✅ 成功访问: {url}")
                return response
            elif response.status_code == 429:
                print(f"⚠️ 请求频率限制: {url}")
                return None
            else:
                print(f"❌ 请求失败: {url}, 状态码: {response.status_code}")
                return None
                
        except requests.exceptions.Timeout:
            print(f"⏰ 请求超时: {url}")
            return None
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求异常: {url}, 错误: {e}")
            return None
    
    def parse_17500_format(self, content: str) -> List[Dict[str, Any]]:
        """解析17500格式的数据"""
        records = []
        lines = content.strip().split('\n')

        for line in lines:
            line = line.strip()
            if not line:
                continue

            try:
                parts = line.split()
                if len(parts) >= 3:
                    issue = parts[0]
                    date = parts[1]

                    # 新格式：第3、4、5列是百位、十位、个位
                    if len(parts) >= 5:
                        hundreds = int(parts[2])
                        tens = int(parts[3])
                        units = int(parts[4])
                    else:
                        # 旧格式：第3列是3位数字
                        numbers = parts[2]
                        if len(numbers) == 3 and numbers.isdigit():
                            hundreds = int(numbers[0])
                            tens = int(numbers[1])
                            units = int(numbers[2])
                        else:
                            continue

                    # 验证数字范围
                    if not (0 <= hundreds <= 9 and 0 <= tens <= 9 and 0 <= units <= 9):
                        continue

                    # 计算和值和跨度
                    sum_value = hundreds + tens + units
                    span = max(hundreds, tens, units) - min(hundreds, tens, units)

                    # 判断号码类型
                    if hundreds == tens == units:
                        number_type = "豹子"
                    elif hundreds == tens or tens == units or hundreds == units:
                        number_type = "对子"
                    else:
                        number_type = "组六"

                    record = {
                        'issue': issue,
                        'draw_date': date,
                        'hundreds': hundreds,
                        'tens': tens,
                        'units': units,
                        'sum_value': sum_value,
                        'span': span,
                        'number_type': number_type
                    }

                    records.append(record)

            except (ValueError, IndexError) as e:
                print(f"解析数据错误: {e}, 行数据: {line}")
                continue

        return records
    
    def get_latest_issue_from_db(self) -> Optional[str]:
        """从数据库获取最新期号"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("SELECT issue FROM lottery_data ORDER BY issue DESC LIMIT 1")
            result = cursor.fetchone()
            conn.close()
            
            return result[0] if result else None
            
        except Exception as e:
            print(f"获取最新期号失败: {e}")
            return None
    
    def fetch_latest_data(self) -> List[Dict[str, Any]]:
        """获取最新数据"""
        print("🔄 开始获取最新数据...")
        
        for i, url in enumerate(self.data_sources):
            print(f"\n📡 尝试数据源 {i+1}/{len(self.data_sources)}: {url}")
            
            response = self.safe_request(url)
            if response:
                try:
                    records = self.parse_17500_format(response.text)
                    if records:
                        print(f"✅ 成功解析 {len(records)} 条记录")
                        return records
                    else:
                        print("❌ 解析结果为空")
                except Exception as e:
                    print(f"❌ 解析失败: {e}")
            
            # 如果不是最后一个数据源，等待更长时间
            if i < len(self.data_sources) - 1:
                wait_time = random.uniform(10, 20)
                print(f"⏳ 等待 {wait_time:.1f} 秒后尝试下一个数据源...")
                time.sleep(wait_time)
        
        print("❌ 所有数据源都无法访问")
        return []
    
    def save_new_records(self, records: List[Dict[str, Any]]) -> int:
        """保存新记录到数据库"""
        if not records:
            return 0
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 获取数据库中最新期号
            latest_issue = self.get_latest_issue_from_db()
            latest_issue_num = int(latest_issue) if latest_issue else 0
            
            new_count = 0
            update_count = 0
            
            for record in records:
                issue_num = int(record['issue'])
                
                # 只处理比数据库最新期号更新的数据
                if issue_num > latest_issue_num:
                    try:
                        cursor.execute("""
                            INSERT OR REPLACE INTO lottery_data (
                                issue, draw_date, hundreds, tens, units,
                                sum_value, span, number_type, created_at, updated_at
                            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        """, (
                            record['issue'],
                            record['draw_date'],
                            record['hundreds'],
                            record['tens'],
                            record['units'],
                            record['sum_value'],
                            record['span'],
                            record['number_type'],
                            datetime.now().isoformat(),
                            datetime.now().isoformat()
                        ))
                        
                        if cursor.rowcount > 0:
                            new_count += 1
                            print(f"✅ 新增: {record['issue']} - {record['draw_date']} - {record['hundreds']}{record['tens']}{record['units']}")
                        
                    except sqlite3.IntegrityError:
                        # 记录已存在，尝试更新
                        cursor.execute("""
                            UPDATE lottery_data SET
                                draw_date = ?, hundreds = ?, tens = ?, units = ?,
                                sum_value = ?, span = ?, number_type = ?, updated_at = ?
                            WHERE issue = ?
                        """, (
                            record['draw_date'],
                            record['hundreds'],
                            record['tens'],
                            record['units'],
                            record['sum_value'],
                            record['span'],
                            record['number_type'],
                            datetime.now().isoformat(),
                            record['issue']
                        ))
                        
                        if cursor.rowcount > 0:
                            update_count += 1
                            print(f"🔄 更新: {record['issue']}")
            
            conn.commit()
            conn.close()
            
            print(f"\n📊 数据更新完成: 新增 {new_count} 条，更新 {update_count} 条")
            return new_count + update_count
            
        except Exception as e:
            print(f"❌ 保存数据失败: {e}")
            return 0
    
    def fix_data_update(self) -> bool:
        """修复数据更新"""
        print("🔧 开始修复数据更新...")
        
        # 获取当前数据库状态
        latest_issue = self.get_latest_issue_from_db()
        print(f"📊 数据库最新期号: {latest_issue}")
        
        # 获取最新数据
        records = self.fetch_latest_data()
        
        if records:
            # 显示数据源最新期号
            latest_source_issue = records[0]['issue'] if records else None
            print(f"📡 数据源最新期号: {latest_source_issue}")
            
            # 保存新记录
            saved_count = self.save_new_records(records)
            
            if saved_count > 0:
                print(f"✅ 数据更新成功，处理了 {saved_count} 条记录")
                return True
            else:
                print("ℹ️ 没有新数据需要更新")
                return True
        else:
            print("❌ 无法获取数据源数据")
            return False
    
    def create_prediction_tables(self):
        """创建预测记录表"""
        try:
            conn = sqlite3.connect("data/fucai3d.db")
            cursor = conn.cursor()
            
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS prediction_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    issue TEXT UNIQUE NOT NULL,
                    prediction_time TEXT NOT NULL,
                    prediction_data TEXT NOT NULL,
                    model_version TEXT,
                    actual_result TEXT,
                    accuracy_score REAL,
                    reviewed BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            conn.commit()
            conn.close()
            print("✅ 预测记录表创建完成")
            
        except Exception as e:
            print(f"❌ 创建预测记录表失败: {e}")

def main():
    """主函数"""
    print("🚀 启动数据更新修复程序")
    
    fixer = DataUpdateFixer()
    
    # 创建必要的表
    fixer.create_prediction_tables()
    
    # 修复数据更新
    success = fixer.fix_data_update()
    
    if success:
        print("\n🎉 数据更新修复完成！")
        print("💡 建议：")
        print("1. 运行闭环自动化系统: python src/automation/closed_loop_system.py")
        print("2. 检查Web界面的最新数据")
        print("3. 验证预测功能是否正常")
    else:
        print("\n❌ 数据更新修复失败")
        print("💡 建议：")
        print("1. 检查网络连接")
        print("2. 稍后重试")
        print("3. 联系技术支持")

if __name__ == "__main__":
    main()
