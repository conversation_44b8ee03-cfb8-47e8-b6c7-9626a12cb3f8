#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
福彩3D预测演示
展示闭环系统的预测能力
"""

import os
import sys
import sqlite3
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(__file__))

from src.fusion.fusion_predictor import FusionPredictor

def demo_prediction():
    """演示预测功能"""
    print("🎯 福彩3D智能预测演示")
    print("=" * 50)
    
    # 检查最新数据
    print("📊 检查数据库状态...")
    try:
        conn = sqlite3.connect('data/lottery.db')
        cursor = conn.cursor()
        
        cursor.execute('SELECT issue, draw_date, hundreds, tens, units FROM lottery_data ORDER BY issue DESC LIMIT 5')
        results = cursor.fetchall()
        
        print("最新5期开奖数据:")
        for row in results:
            print(f"  {row[0]} ({row[1]}): {row[2]}{row[3]}{row[4]}")
        
        # 获取下一期期号
        latest_issue = results[0][0]
        next_issue = str(int(latest_issue) + 1)
        print(f"\n🎲 预测期号: {next_issue}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 数据库检查失败: {e}")
        return
    
    # 初始化融合预测器
    print("\n⚙️ 初始化预测系统...")
    try:
        predictor = FusionPredictor('data/fucai3d.db')
        print("✅ 预测系统初始化完成")
    except Exception as e:
        print(f"❌ 预测系统初始化失败: {e}")
        return
    
    # 执行预测
    print(f"\n🔮 开始预测 {next_issue} 期...")
    try:
        result = predictor.predict_next_period(next_issue)
        
        if result:
            print("✅ 预测完成！")
            print("\n🎯 预测结果:")

            # 检查是否有推荐结果
            recommendations = result.get('recommendations', [])
            if recommendations:
                print(f"  生成了 {len(recommendations)} 个推荐组合")
                print("\n🏆 前5个推荐:")
                for i, rec in enumerate(recommendations[:5], 1):
                    hundreds = rec.get('hundreds', '?')
                    tens = rec.get('tens', '?')
                    units = rec.get('units', '?')
                    score = rec.get('score', 0)
                    print(f"  {i}. {hundreds}{tens}{units} (评分: {score:.3f})")

                # 显示最佳预测
                best_rec = recommendations[0]
                print(f"\n🎯 最佳预测: {best_rec.get('hundreds', '?')}{best_rec.get('tens', '?')}{best_rec.get('units', '?')}")
                print(f"  综合评分: {best_rec.get('score', 0):.3f}")

            # 显示融合信息
            fusion_info = result.get('fusion_info', {})
            if fusion_info:
                print("\n🔗 融合信息:")
                print(f"  融合方法: {fusion_info.get('method', 'adaptive_fusion')}")
                print(f"  生成组合数: {fusion_info.get('total_combinations', 0)}")
                print(f"  优化后数量: {fusion_info.get('optimized_count', 0)}")

            print(f"\n⏰ 预测时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"📝 预测记录已保存到数据库")

        else:
            print("❌ 预测失败")
            print(f"错误信息: 无返回结果")
            
    except Exception as e:
        print(f"❌ 预测过程异常: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 50)
    print("🎉 预测演示完成")
    print("\n💡 说明:")
    print("- 这是基于历史数据的智能预测")
    print("- 预测结果仅供参考，不构成投注建议")
    print("- 系统会持续学习和优化预测算法")
    print("- 闭环系统将自动进行复盘和优化")

if __name__ == "__main__":
    demo_prediction()
