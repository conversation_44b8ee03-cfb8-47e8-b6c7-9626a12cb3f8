#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import requests
from datetime import datetime

def check_database_status():
    """检查数据库状态"""
    print("=== 数据库状态检查 ===")

    # 检查所有数据库文件
    db_files = ['data/fucai3d.db', 'data/lottery.db', 'data/alerts.db']

    for db_file in db_files:
        print(f"\n--- 检查 {db_file} ---")
        try:
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()

            # 检查所有表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            print(f"数据库表: {[table[0] for table in tables]}")

            # 检查可能的数据表
            data_tables = ['historical_data', 'lottery_data', 'lottery_records']
            found_data = False

            for table_name in data_tables:
                if (table_name,) in tables:
                    cursor.execute(f'SELECT COUNT(*) FROM {table_name}')
                    count = cursor.fetchone()[0]
                    print(f"{table_name}表记录数: {count}")

                    if count > 0:
                        # 尝试获取表结构
                        cursor.execute(f'PRAGMA table_info({table_name})')
                        columns = cursor.fetchall()
                        print(f"{table_name}表结构: {[col[1] for col in columns]}")

                        # 尝试获取最新数据
                        try:
                            if 'issue' in [col[1] for col in columns]:
                                cursor.execute(f'SELECT * FROM {table_name} ORDER BY issue DESC LIMIT 3')
                            else:
                                cursor.execute(f'SELECT * FROM {table_name} ORDER BY rowid DESC LIMIT 3')
                            results = cursor.fetchall()
                            print(f"\n{table_name}最新3条数据:")
                            for row in results:
                                print(f"  {row}")
                            found_data = True
                        except Exception as e:
                            print(f"查询{table_name}数据失败: {e}")

            if not found_data:
                print("❌ 未找到任何历史数据表或数据为空！")

            conn.close()

        except Exception as e:
            print(f"检查{db_file}失败: {e}")

def check_data_source():
    """检查数据源状态"""
    print("\n=== 数据源状态检查 ===")
    
    url = "https://data.17500.cn/3d_asc.txt"
    try:
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            lines = response.text.strip().split('\n')
            print(f"数据源可用，共 {len(lines)} 条记录")
            
            # 显示最新几期
            print("\n数据源最新5期:")
            for line in lines[:5]:
                parts = line.split()
                if len(parts) >= 3:
                    print(f"期号: {parts[0]}, 日期: {parts[1]}, 号码: {parts[2]}")
        else:
            print(f"数据源不可用，状态码: {response.status_code}")
            
    except Exception as e:
        print(f"数据源检查失败: {e}")

def check_auto_update_status():
    """检查自动更新状态"""
    print("\n=== 自动更新状态检查 ===")
    
    # 检查是否有定时任务在运行
    try:
        import schedule
        jobs = schedule.jobs
        print(f"当前定时任务数: {len(jobs)}")
        for job in jobs:
            print(f"- {job}")
    except ImportError:
        print("schedule库未安装")
    except Exception as e:
        print(f"定时任务检查失败: {e}")

if __name__ == "__main__":
    check_database_status()
    check_data_source()
    check_auto_update_status()
