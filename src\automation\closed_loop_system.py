#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
福彩3D闭环自动化系统
实现真正的闭环：数据更新 -> 预测 -> 复盘 -> 优化 -> 迭代
"""

import os
import sys
import time
import sqlite3
import requests
import schedule
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import json

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from src.data.updater import smart_incremental_update
from src.predictors.unified_predictor_interface import UnifiedPredictorInterface
from src.fusion.fusion_predictor import FusionPredictor
from src.optimization.intelligent_closed_loop_optimizer import IntelligentClosedLoopOptimizer

class ClosedLoopSystem:
    """福彩3D闭环自动化系统"""
    
    def __init__(self):
        self.logger = self._setup_logger()
        self.db_path = "data/lottery.db"
        self.fusion_db_path = "data/fucai3d.db"
        
        # 初始化核心组件
        self.predictor_interface = UnifiedPredictorInterface(self.db_path)
        self.fusion_predictor = FusionPredictor(self.fusion_db_path)
        self.optimizer = IntelligentClosedLoopOptimizer(self.fusion_db_path)
        
        # 闭环配置
        self.config = {
            'data_update_time': "21:35",  # 每天21:35更新数据
            'prediction_time': "21:40",   # 21:40进行预测
            'review_time': "22:00",       # 22:00进行复盘
            'optimization_time': "02:00", # 凌晨2:00进行优化
            'max_retries': 3,
            'retry_delay': 300,  # 5分钟
        }
        
        self.logger.info("🚀 福彩3D闭环自动化系统初始化完成")
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志系统"""
        logger = logging.getLogger('ClosedLoopSystem')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def auto_data_update(self) -> bool:
        """自动数据更新"""
        self.logger.info("🔄 开始自动数据更新...")
        
        try:
            # 使用智能增量更新
            success = smart_incremental_update()
            
            if success:
                self.logger.info("✅ 数据更新成功")
                
                # 检查是否有新数据
                new_count = self._check_new_data()
                if new_count > 0:
                    self.logger.info(f"📊 发现 {new_count} 条新数据，触发预测流程")
                    # 延迟5分钟后进行预测
                    schedule.every().minute.do(self.auto_prediction).tag('one_time')
                    return True
                else:
                    self.logger.info("📊 暂无新数据")
                    return True
            else:
                self.logger.error("❌ 数据更新失败")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ 数据更新异常: {e}")
            return False
    
    def _check_new_data(self) -> int:
        """检查新数据数量"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 检查今天是否有新数据
            today = datetime.now().strftime('%Y-%m-%d')
            cursor.execute(
                "SELECT COUNT(*) FROM lottery_data WHERE draw_date = ?",
                (today,)
            )
            count = cursor.fetchone()[0]
            conn.close()
            
            return count
            
        except Exception as e:
            self.logger.error(f"检查新数据失败: {e}")
            return 0
    
    def auto_prediction(self) -> bool:
        """自动预测"""
        self.logger.info("🎯 开始自动预测...")
        
        try:
            # 获取下一期期号
            next_issue = self._get_next_issue()
            if not next_issue:
                self.logger.error("❌ 无法获取下一期期号")
                return False
            
            self.logger.info(f"🎲 预测期号: {next_issue}")
            
            # 执行融合预测
            prediction_result = self.fusion_predictor.predict_next_period(next_issue)
            
            if prediction_result and prediction_result.get('success'):
                self.logger.info("✅ 预测完成")
                self.logger.info(f"🎯 预测结果: {prediction_result.get('final_prediction')}")
                
                # 保存预测记录
                self._save_prediction_record(next_issue, prediction_result)
                return True
            else:
                self.logger.error("❌ 预测失败")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ 预测异常: {e}")
            return False
        finally:
            # 清理一次性任务
            schedule.clear('one_time')
    
    def _get_next_issue(self) -> Optional[str]:
        """获取下一期期号"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute(
                "SELECT issue FROM lottery_data ORDER BY issue DESC LIMIT 1"
            )
            result = cursor.fetchone()
            conn.close()
            
            if result:
                last_issue = result[0]
                # 简单的期号递增逻辑
                next_issue_num = int(last_issue) + 1
                return str(next_issue_num)
            
            return None
            
        except Exception as e:
            self.logger.error(f"获取下一期期号失败: {e}")
            return None
    
    def _save_prediction_record(self, issue: str, prediction_result: Dict[str, Any]):
        """保存预测记录"""
        try:
            conn = sqlite3.connect(self.fusion_db_path)
            cursor = conn.cursor()
            
            # 保存到预测记录表
            cursor.execute("""
                INSERT OR REPLACE INTO prediction_records (
                    issue, prediction_time, prediction_data, model_version
                ) VALUES (?, ?, ?, ?)
            """, (
                issue,
                datetime.now().isoformat(),
                json.dumps(prediction_result),
                "v1.0"
            ))
            
            conn.commit()
            conn.close()
            
            self.logger.info(f"📝 预测记录已保存: {issue}")
            
        except Exception as e:
            self.logger.error(f"保存预测记录失败: {e}")
    
    def auto_review(self) -> bool:
        """自动复盘"""
        self.logger.info("📊 开始自动复盘...")
        
        try:
            # 获取最近的预测和实际结果
            review_results = self._perform_review()
            
            if review_results:
                self.logger.info("✅ 复盘完成")
                self.logger.info(f"📈 准确率: {review_results.get('accuracy', 0):.2%}")
                
                # 如果准确率低于阈值，触发优化
                if review_results.get('accuracy', 0) < 0.6:
                    self.logger.warning("⚠️ 准确率偏低，触发优化流程")
                    schedule.every().minute.do(self.auto_optimization).tag('one_time')
                
                return True
            else:
                self.logger.warning("⚠️ 复盘数据不足")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ 复盘异常: {e}")
            return False
    
    def _perform_review(self) -> Optional[Dict[str, Any]]:
        """执行复盘分析"""
        try:
            # 这里应该实现具体的复盘逻辑
            # 比较预测结果和实际开奖结果
            
            # 模拟复盘结果
            review_results = {
                'accuracy': 0.65,
                'reviewed_count': 10,
                'correct_predictions': 6,
                'improvement_suggestions': [
                    '调整模型权重',
                    '增加特征维度',
                    '优化融合策略'
                ]
            }
            
            return review_results
            
        except Exception as e:
            self.logger.error(f"执行复盘失败: {e}")
            return None
    
    def auto_optimization(self) -> bool:
        """自动优化"""
        self.logger.info("⚙️ 开始自动优化...")
        
        try:
            # 执行智能优化
            optimization_result = self.optimizer.run_optimization_cycle()
            
            if optimization_result and optimization_result.get('success'):
                self.logger.info("✅ 优化完成")
                self.logger.info(f"📈 优化效果: {optimization_result.get('improvement_score', 0):.3f}")
                return True
            else:
                self.logger.error("❌ 优化失败")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ 优化异常: {e}")
            return False
        finally:
            # 清理一次性任务
            schedule.clear('one_time')
    
    def setup_schedule(self):
        """设置定时任务"""
        self.logger.info("⏰ 设置闭环定时任务...")
        
        # 每日数据更新
        schedule.every().day.at(self.config['data_update_time']).do(self.auto_data_update)
        
        # 每日预测（如果有新数据）
        schedule.every().day.at(self.config['prediction_time']).do(self.auto_prediction)
        
        # 每日复盘
        schedule.every().day.at(self.config['review_time']).do(self.auto_review)
        
        # 每日优化
        schedule.every().day.at(self.config['optimization_time']).do(self.auto_optimization)
        
        self.logger.info("✅ 定时任务设置完成")
        self.logger.info(f"📅 数据更新: {self.config['data_update_time']}")
        self.logger.info(f"🎯 自动预测: {self.config['prediction_time']}")
        self.logger.info(f"📊 自动复盘: {self.config['review_time']}")
        self.logger.info(f"⚙️ 自动优化: {self.config['optimization_time']}")
    
    def run(self):
        """运行闭环系统"""
        self.logger.info("🚀 启动福彩3D闭环自动化系统")
        
        # 设置定时任务
        self.setup_schedule()
        
        # 立即执行一次数据更新检查
        self.logger.info("🔄 执行初始数据更新检查...")
        self.auto_data_update()
        
        # 运行调度器
        self.logger.info("⏰ 开始运行定时调度器...")
        while True:
            try:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次
            except KeyboardInterrupt:
                self.logger.info("👋 用户中断，系统退出")
                break
            except Exception as e:
                self.logger.error(f"❌ 调度器异常: {e}")
                time.sleep(60)

if __name__ == "__main__":
    system = ClosedLoopSystem()
    system.run()
